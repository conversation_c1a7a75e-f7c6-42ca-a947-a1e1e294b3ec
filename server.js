const proxmoxApi = require('proxmox-api').default;
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

// Allow self-signed certificates (untuk development)
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Proxmox API configuration
const proxmoxConfig = {
  host: process.env.PROXMOX_HOST || '***************',
  username: process.env.PROXMOX_USERNAME || 'root@pam',
  password: process.env.PROXMOX_PASSWORD || '12344321',
  port: parseInt(process.env.PROXMOX_PORT) || 8006
};

// Initialize Proxmox API client
const proxmox = proxmoxApi(proxmoxConfig);

// Function to get mock data for testing
function getMockData() {
  return {
    host: {
      ip: proxmoxConfig.host,
      platform: 'proxmox-ve',
      uptime: '5d 10h 30m'
    },
    nodes: [
      {
        name: 'pve1',
        status: 'online',
        cpu_usage: '15.50%',
        cpu_cores: 8,
        cpu_model: 'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
        cpu_freq: '3.70 GHz',
        mem_used: '8.5 GB',
        mem_total: '32.0 GB',
        disk_used: '45.2 GB',
        disk_total: '500.0 GB',
        uptime: '5d 10h 30m',
        kernel: '5.15.0-1-pve',
        pve_version: '7.4-3',
        loadavg: [0.15, 0.25, 0.30],
        ip: '***************'
      }
    ],
    timestamp: new Date().toISOString(),
    note: 'Data ini adalah mock data karena tidak dapat terhubung ke server Proxmox'
  };
}

async function getProxmoxData() {
  try {
    // Fetch all nodes menggunakan sintaks API yang benar
    const nodes = await proxmox.nodes.$get();

    if (!nodes || nodes.length === 0) {
      throw new Error('No nodes found in Proxmox cluster');
    }

    // Use the first available node for host uptime
    const firstNodeName = nodes[0].node;
    const hostStatus = await proxmox.nodes.$(firstNodeName).status.$get();

    const nodeDetails = await Promise.all(nodes.map(async (node) => {
      try {
        const nodeStatus = await proxmox.nodes.$(node.node).status.$get();

        return {
          name: node.node,
          status: nodeStatus.status || 'unknown',
          cpu_usage: nodeStatus.cpu ? `${(nodeStatus.cpu * 100).toFixed(2)}%` : 'N/A',
          cpu_cores: nodeStatus.maxcpu || 'N/A',
          cpu_model: nodeStatus.cpuinfo?.model || 'N/A',
          cpu_freq: nodeStatus.cpuinfo?.mhz ? `${(nodeStatus.cpuinfo.mhz / 1000).toFixed(2)} GHz` : 'N/A',
          mem_used: nodeStatus.memory?.used ? `${(nodeStatus.memory.used / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          mem_total: nodeStatus.memory?.total ? `${(nodeStatus.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          disk_used: nodeStatus.rootfs?.used ? `${(nodeStatus.rootfs.used / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          disk_total: nodeStatus.rootfs?.total ? `${(nodeStatus.rootfs.total / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          uptime: nodeStatus.uptime ? formatUptime(nodeStatus.uptime) : 'N/A',
          kernel: nodeStatus.kversion || 'N/A',
          pve_version: nodeStatus.version || 'N/A',
          loadavg: nodeStatus.loadavg || 'N/A',
          ip: getNodeIp(node.node)
        };
      } catch (nodeError) {
        console.error(`Error fetching data for node ${node.node}:`, nodeError);
        return {
          name: node.node,
          status: 'error',
          error: 'Failed to fetch node data'
        };
      }
    }));

    return {
      host: {
        ip: proxmoxConfig.host,
        platform: 'proxmox-ve',
        uptime: hostStatus?.uptime ? formatUptime(hostStatus.uptime) : 'N/A'
      },
      nodes: nodeDetails,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching Proxmox data:', error);
    console.log('Menggunakan mock data karena tidak dapat terhubung ke Proxmox server');

    // Return mock data jika tidak bisa connect ke Proxmox
    return getMockData();
  }
}

// Helper function to format uptime
function formatUptime(seconds) {
  const days = Math.floor(seconds / (3600 * 24));
  seconds %= 3600 * 24;
  const hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  const minutes = Math.floor(seconds / 60);
  return `${days}d ${hours}h ${minutes}m`;
}

// Helper function to get node IP (mock implementation)
function getNodeIp(nodeName) {
  // In a real scenario, you would fetch this from Proxmox API or a configuration
  // This is a mock implementation; replace with actual IP retrieval logic
  const ipMap = {
    pve1: '************',
    pve2: '************' // Add more nodes as needed
  };
  return ipMap[nodeName] || 'Unknown';
}

// Main API endpoint - menampilkan semua data Proxmox
app.get('/api', async (req, res) => {
  try {
    const data = await getProxmoxData();
    res.json(data);
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: 'Failed to fetch Proxmox data',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 404 handler untuk endpoint lain
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Endpoint ${req.originalUrl} tidak tersedia. Gunakan /api untuk mendapatkan data Proxmox.`,
    availableEndpoint: '/api'
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: 'Terjadi kesalahan yang tidak terduga'
  });
});

// Start the server
app.listen(port, () => {
  console.log(`🚀 Proxmox Monitor API server berjalan di http://localhost:${port}`);
  console.log(`📊 Endpoint tersedia:`);
  console.log(`   - Proxmox data: http://localhost:${port}/api`);
});