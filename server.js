const { proxmoxApi } = require('proxmox-api');
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Proxmox API configuration
const proxmoxConfig = {
  host: process.env.PROXMOX_HOST || '***************',
  username: process.env.PROXMOX_USERNAME || 'root@pam',
  password: process.env.PROXMOX_PASSWORD || '12344321',
  port: parseInt(process.env.PROXMOX_PORT) || 8006
};

// Initialize Proxmox API client
const proxmox = proxmoxApi(proxmoxConfig);

async function getProxmoxData() {
  try {
    // Authenticate with Proxmox API
    await proxmox.login();

    // Fetch all nodes first to get the first node name dynamically
    const nodes = await proxmox.get('/nodes');

    if (!nodes.data || nodes.data.length === 0) {
      throw new Error('No nodes found in Proxmox cluster');
    }

    // Use the first available node for uptime
    const firstNodeName = nodes.data[0].node;
    const uptime = await proxmox.get(`/nodes/${firstNodeName}/status`);

    const nodeDetails = await Promise.all(nodes.data.map(async (node) => {
      try {
        const nodeStatus = await proxmox.get(`/nodes/${node.node}/status`);
        const nodeInfo = nodeStatus.data;

        return {
          name: node.node,
          status: nodeInfo.status || 'unknown',
          cpu_usage: nodeInfo.cpu ? `${(nodeInfo.cpu * 100).toFixed(2)}%` : 'N/A',
          cpu_cores: nodeInfo.maxcpu || 'N/A',
          cpu_model: nodeInfo.cpuinfo?.model || 'N/A',
          cpu_freq: nodeInfo.cpuinfo?.mhz ? `${(nodeInfo.cpuinfo.mhz / 1000).toFixed(2)} GHz` : 'N/A',
          mem_used: nodeInfo.memory?.used ? `${(nodeInfo.memory.used / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          mem_total: nodeInfo.memory?.total ? `${(nodeInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          disk_used: nodeInfo.rootfs?.used ? `${(nodeInfo.rootfs.used / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          disk_total: nodeInfo.rootfs?.total ? `${(nodeInfo.rootfs.total / 1024 / 1024 / 1024).toFixed(1)} GB` : 'N/A',
          uptime: nodeInfo.uptime ? formatUptime(nodeInfo.uptime) : 'N/A',
          kernel: nodeInfo.kversion || 'N/A',
          pve_version: nodeInfo.version || 'N/A',
          loadavg: nodeInfo.loadavg || 'N/A',
          ip: getNodeIp(node.node)
        };
      } catch (nodeError) {
        console.error(`Error fetching data for node ${node.node}:`, nodeError);
        return {
          name: node.node,
          status: 'error',
          error: 'Failed to fetch node data'
        };
      }
    }));

    return {
      host: {
        ip: proxmoxConfig.host.split(':')[0], // Remove port from IP
        platform: 'proxmox-ve',
        uptime: uptime.data?.uptime ? formatUptime(uptime.data.uptime) : 'N/A'
      },
      nodes: nodeDetails,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching Proxmox data:', error);
    throw new Error(`Failed to fetch Proxmox data: ${error.message}`);
  }
}

// Helper function to format uptime
function formatUptime(seconds) {
  const days = Math.floor(seconds / (3600 * 24));
  seconds %= 3600 * 24;
  const hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  const minutes = Math.floor(seconds / 60);
  return `${days}d ${hours}h ${minutes}m`;
}

// Helper function to get node IP (mock implementation)
function getNodeIp(nodeName) {
  // In a real scenario, you would fetch this from Proxmox API or a configuration
  // This is a mock implementation; replace with actual IP retrieval logic
  const ipMap = {
    pve1: '************',
    pve2: '************' // Add more nodes as needed
  };
  return ipMap[nodeName] || 'Unknown';
}

// Main API endpoint - menampilkan semua data Proxmox
app.get('/api', async (req, res) => {
  try {
    const data = await getProxmoxData();
    res.json(data);
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: 'Failed to fetch Proxmox data',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 404 handler untuk endpoint lain
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Endpoint ${req.originalUrl} tidak tersedia. Gunakan /api untuk mendapatkan data Proxmox.`,
    availableEndpoint: '/api'
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: 'Terjadi kesalahan yang tidak terduga'
  });
});

// Start the server
app.listen(port, () => {
  console.log(`🚀 Proxmox Monitor API server berjalan di http://localhost:${port}`);
  console.log(`📊 Endpoint tersedia:`);
  console.log(`   - Proxmox data: http://localhost:${port}/api`);
});